# CORS问题解决方案

## 问题描述
前端应用运行在 `http://localhost:5173`，后端API在 `http://************:8080`，导致跨域请求被浏览器阻止。

## 解决方案

### 1. 前端代理配置（推荐）
已在 `vite.config.ts` 中配置开发服务器代理：

```typescript
server: {
  proxy: {
    '/captchaImage': {
      target: 'http://************:8080',
      changeOrigin: true,
      secure: false,
    },
    '/login': {
      target: 'http://************:8080',
      changeOrigin: true,
      secure: false,
    },
    // ... 其他API路径
  }
}
```

### 2. 环境配置
- 开发环境：使用代理，baseURL为空字符串
- 生产环境：直接使用完整的API地址

### 3. 网络优化
- 添加了重试机制
- 优化了错误处理
- 减少了超时时间

## 使用方法

1. **重启开发服务器**：
   ```bash
   npm run dev
   ```

2. **检查代理是否生效**：
   - 打开浏览器开发者工具
   - 查看Network标签
   - 验证码请求应该显示为 `http://localhost:5173/captchaImage`

## 后端CORS配置（可选）
如果需要在后端配置CORS，可以添加以下响应头：

```
Access-Control-Allow-Origin: http://localhost:5173
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Allow-Credentials: true
```

## 故障排除

1. **验证码仍然加载慢**：
   - 检查后端服务是否正常运行
   - 检查网络连接
   - 查看控制台是否有其他错误

2. **代理不生效**：
   - 确保重启了开发服务器
   - 检查vite.config.ts配置是否正确
   - 确认请求路径与代理配置匹配

3. **生产环境部署**：
   - 确保生产环境的API地址正确
   - 考虑使用nginx反向代理
   - 或者在后端配置CORS
