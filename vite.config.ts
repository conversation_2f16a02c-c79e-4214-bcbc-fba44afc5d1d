import { resolve } from 'path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    alias: {
      // 为了使@导入别名像在 Vue CLI 中那样工作，我们需要添加这一点。
      '@': resolve(__dirname, './src'),
    },
  },
  define: {
    // 修复 docx 库在浏览器环境中的 global 未定义问题
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['docx']
  },
  // 开发服务器配置
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173,
    // 代理配置解决CORS问题
    proxy: {
      // 代理所有以 /api 开头的请求
      '/api': {
        target: 'http://************:8080',
        changeOrigin: true,
        secure: false,
        // 可选：重写路径，如果后端不需要 /api 前缀
        // rewrite: (path) => path.replace(/^\/api/, '')
      },
      // 代理验证码请求
      '/captchaImage': {
        target: 'http://************:8080',
        changeOrigin: true,
        secure: false,
      },
      // 代理登录请求
      '/login': {
        target: 'http://************:8080',
        changeOrigin: true,
        secure: false,
      },
      // 代理退出登录请求
      '/logout': {
        target: 'http://************:8080',
        changeOrigin: true,
        secure: false,
      }
    }
  }
})
